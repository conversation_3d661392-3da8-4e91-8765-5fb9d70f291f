'use client';

import { useEffect, useRef } from 'react';
import { useReaderStore } from './store';

export default function ReaderCanvas({ src = '/books/example.epub' }: { src?: string }) {
  const hostRef = useRef<HTMLDivElement>(null);

  // ✅ separate selectors (no array/object selector)
  const setView          = useReaderStore(s => s.setView);
  const setMeta          = useReaderStore(s => s.setMeta);
  const setTOC           = useReaderStore(s => s.setTOC);
  const setSectionCount  = useReaderStore(s => s.setSectionCount);
  const setProgress      = useReaderStore(s => s.setProgress);
  const flow             = useReaderStore(s => s.flow);
  const view             = useReaderStore(s => s.view);

  useEffect(() => {
    let cancelled = false;
    (async () => {
      if (!customElements.get('foliate-view')) {
        await import('@/public/foliate-js/view.js');
      }
      if (cancelled) return;

      const el = document.createElement('foliate-view') as any;
      el.style.width = '100%';
      el.style.height = '100%';

      el.setAttribute('flow', flow);
      el.setAttribute('gap', '6%');
      el.setAttribute('margin', '56px');
      el.setAttribute('max-inline-size', '720px');
      el.setAttribute('max-block-size', '1000px');
      el.setAttribute('max-column-count', '2');
      el.setAttribute('animated', '');

      hostRef.current!.appendChild(el);
      setView(el);

      const onLoad = (e: any) => {
        const index = e.detail?.index ?? 0;
        setProgress(index, 0, false);
        try {
          const book = (el as any).book;
          if (book) {
            setMeta({
              title: book?.metadata?.title,
              author: Array.isArray(book?.metadata?.author)
                ? book.metadata.author[0]?.name ?? ''
                : book?.metadata?.author?.name ?? book?.metadata?.author,
              language: book?.metadata?.language,
              coverUrl: '',
            });
            setTOC(
              (book?.toc ?? []).map((t: any) => ({
                label: t.label,
                href: t.href,
                level: 0,
                children: t.subitems?.map((c: any) => ({ label: c.label, href: c.href })) ?? [],
              }))
            );
            setSectionCount(book?.sections?.length ?? 0);
          }
        } catch {}
        el.removeEventListener('load', onLoad);
      };
      el.addEventListener('load', onLoad);

      const onRelocate = (e: any) => {
        const idx = e.detail?.index ?? 0;
        const frac = e.detail?.fraction ?? 0;
        setProgress(idx, frac, false);
      };
      el.addEventListener('relocate', onRelocate);

      await el.open(src);
    })();
    return () => {
      cancelled = true;
    };
  }, [src, flow, setMeta, setProgress, setTOC, setView, setSectionCount]);

  return (
    <div className="relative h-full">
      <div ref={hostRef} className="absolute inset-0" />
      <button
        aria-label="Previous page"
        onClick={() => view?.prev()}
        className="group absolute inset-y-0 left-0 w-[18%] cursor-pointer bg-transparent hover:bg-white/5 transition-opacity"
      />
      <button
        aria-label="Next page"
        onClick={() => view?.next()}
        className="group absolute inset-y-0 right-0 w-[18%] cursor-pointer bg-transparent hover:bg-white/5 transition-opacity"
      />
    </div>
  );
}
