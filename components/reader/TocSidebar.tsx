'use client';

import { useRouter } from 'next/navigation';
import { BookOpen, Search, Menu, X, Bookmark, List } from 'lucide-react';
import { useReaderStore } from './store';
import { shallow } from 'zustand/shallow';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger, Ta<PERSON>Content } from '@/components/ui/tabs';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';

export default function TocSidebar() {
    const router = useRouter();
    const isOpen = useReaderStore((s) => s.isSidebarOpen);
    const toggleSidebar = useReaderStore((s) => s.toggleSidebar);
    const toc = useReaderStore((s) => s.toc);
    const view = useReaderStore((s) => s.view);
    const title = useReaderStore((s) => s.title);
    const author = useReaderStore((s) => s.author);
    const bookmarks = useReaderStore((s) => s.bookmarks);


  return (
    <aside
      className={`h-full border-r bg-background/95 backdrop-blur ${isOpen ? 'block w-80' : 'hidden lg:block lg:w-0 lg:border-none'}`}
      role="navigation"
      aria-label="Table of contents"
    >
      <div className="flex items-center gap-2 p-3">
        <Button variant="ghost" size="icon" onClick={() => router.push('/')}>
          <BookOpen className="h-5 w-5" />
        </Button>
        <Button variant="ghost" size="icon">
          <Search className="h-5 w-5" />
        </Button>
        <Button variant="ghost" size="icon">
          <Menu className="h-5 w-5" />
        </Button>
        <div className="ml-auto">
          <Button variant="ghost" size="icon" onClick={() => toggleSidebar(false)}>
            <X className="h-5 w-5" />
          </Button>
        </div>
      </div>

      <div className="px-3 pb-3">
        <div className="flex gap-3 items-center">
          <div className="h-16 w-12 rounded bg-muted" />
          <div className="min-w-0">
            <div className="text-sm font-semibold truncate">{title ?? 'Untitled'}</div>
            <div className="text-xs text-muted-foreground truncate">{author ?? '—'}</div>
          </div>
        </div>
      </div>
      <Separator />

      <Tabs defaultValue="chapters" className="h-[calc(100%-120px)] flex flex-col">
        <TabsList className="mx-3 mt-2">
          <TabsTrigger value="chapters"><List className="h-4 w-4 mr-1" />Chapters</TabsTrigger>
          <TabsTrigger value="annotations">Annotations</TabsTrigger>
          <TabsTrigger value="bookmarks"><Bookmark className="h-4 w-4 mr-1" />Bookmarks</TabsTrigger>
        </TabsList>

        <TabsContent value="chapters" className="flex-1 overflow-hidden">
          <ScrollArea className="h-full px-2 pb-2">
            <ul className="space-y-0.5">
              {toc?.map((item, i) => (
                <li key={`${item.href || item.label}-${i}`}>
                  <button
                    className="w-full grid grid-cols-[1fr_auto] items-center gap-2 rounded px-2 py-1.5 hover:bg-muted text-left"
                    onClick={() => view?.goTo(i)}
                  >
                    <span className="truncate">{item.label}</span>
                    <span className="text-xs text-muted-foreground tabular-nums">{i + 1}</span>
                  </button>
                </li>
              ))}
            </ul>
          </ScrollArea>
        </TabsContent>

        <TabsContent value="annotations" className="flex-1 px-3 py-2 text-sm text-muted-foreground">
          (No annotations yet)
        </TabsContent>

        <TabsContent value="bookmarks" className="flex-1 overflow-hidden">
          <ScrollArea className="h-full px-2 pb-2">
            {bookmarks.length === 0 ? (
              <div className="px-2 py-3 text-sm text-muted-foreground">No bookmarks</div>
            ) : (
              <ul className="space-y-1">
                {bookmarks.map((b) => (
                  <li key={b.id}>
                    <button
                      className="w-full rounded px-2 py-1.5 hover:bg-muted text-left text-sm"
                      onClick={() => view?.goTo({ index: b.index })}
                    >
                      {b.label ?? `Location ${b.index + 1}`}
                    </button>
                  </li>
                ))}
              </ul>
            )}
          </ScrollArea>
        </TabsContent>
      </Tabs>
    </aside>
  );
}
