'use client';

import { create } from 'zustand';
import { persist } from 'zustand/middleware';

export type FoliateView = HTMLElement & {
  open(src: string | File | Blob): Promise<void>;
  goTo(target: string | number | { index: number; anchor?: any }): Promise<any>;
  prev(): void;
  next(): void;
};

type TOCItem = { label: string; href?: string; index?: number; level?: number; children?: TOCItem[] };

type ReaderState = {
  // refs
  view: FoliateView | null;

  // ui
  isSidebarOpen: boolean;
  showChrome: boolean; // header/footer reveal

  // book meta
  bookId?: string;
  title?: string;
  author?: string;
  coverUrl?: string;
  language?: string;
  toc: TOCItem[];
  sectionCount: number;

  // progress
  sectionIndex: number;
  sectionFraction: number;

  // history for Back/Forward (not prev/next page)
  history: Array<{ index: number; fraction: number }>;
  historyCursor: number;

  // user data
  bookmarks: Array<{ id: string; index: number; fraction: number; label?: string }>;
  notes: Array<{ id: string; index: number; fraction: number; text: string }>;

  // prefs (you can expand later)
  fontFamily: string;
  fontSizePct: number;
  flow: 'paginated' | 'scrolled';

  // actions
  setView: (v: FoliateView | null) => void;
  toggleSidebar: (open?: boolean) => void;
  setShowChrome: (show: boolean) => void;

  setMeta: (m: Partial<ReaderState>) => void;
  setTOC: (toc: TOCItem[]) => void;
  setSectionCount: (n: number) => void;

  setProgress: (index: number, fraction: number, pushHistory?: boolean) => void;
  navBack: () => void;
  navForward: () => void;

  addBookmarkAt: (index: number, fraction: number, label?: string) => void;
  removeBookmark: (id: string) => void;

  setPref: (k: keyof Pick<ReaderState, 'fontFamily'|'fontSizePct'|'flow'>, v: any) => void;
};

export const useReaderStore = create<ReaderState>()(
  persist(
    (set, get) => ({
      view: null,
      isSidebarOpen: true,
      showChrome: false,

      title: undefined,
      author: undefined,
      coverUrl: undefined,
      language: undefined,
      toc: [],
      sectionCount: 0,

      sectionIndex: 0,
      sectionFraction: 0,

      history: [],
      historyCursor: -1,

      bookmarks: [],
      notes: [],

      fontFamily: 'system-ui',
      fontSizePct: 100,
      flow: 'paginated',

      setView: (v) => set({ view: v }),
      toggleSidebar: (open) => set((s) => ({ isSidebarOpen: open ?? !s.isSidebarOpen })),
      setShowChrome: (show) => set({ showChrome: show }),

      setMeta: (m) => set(m),
      setTOC: (toc) => set({ toc }),
      setSectionCount: (n) => set({ sectionCount: n }),

      setProgress: (index, fraction, pushHistory = false) =>
        set((s) => {
          const next = { sectionIndex: index, sectionFraction: fraction };
          if (!pushHistory) return next;
          const entry = { index, fraction };
          const trimmed = s.history.slice(0, s.historyCursor + 1);
          return {
            ...next,
            history: [...trimmed, entry],
            historyCursor: trimmed.length,
          };
        }),

      navBack: () => {
        const s = get();
        if (s.historyCursor <= 0) return;
        const target = s.history[s.historyCursor - 1];
        s.view?.goTo({ index: target.index });
        set({ historyCursor: s.historyCursor - 1 });
      },

      navForward: () => {
        const s = get();
        if (s.historyCursor >= s.history.length - 1) return;
        const target = s.history[s.historyCursor + 1];
        s.view?.goTo({ index: target.index });
        set({ historyCursor: s.historyCursor + 1 });
      },

      addBookmarkAt: (index, fraction, label) =>
        set((s) => ({
          bookmarks: [
            ...s.bookmarks,
            { id: crypto.randomUUID(), index, fraction, label },
          ],
        })),
      removeBookmark: (id) =>
        set((s) => ({ bookmarks: s.bookmarks.filter((b) => b.id !== id) })),

      setPref: (k, v) => set({ [k]: v } as any),
    }),
    { name: 'reader-state' }
  )
);
