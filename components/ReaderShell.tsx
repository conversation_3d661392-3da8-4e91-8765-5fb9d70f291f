'use client';

import Toc<PERSON><PERSON>bar from './reader/TocSidebar';
import ReaderCanvas from './reader/ReaderCanvas';
import ReaderHeader from './reader/ReaderHeader';
import ReaderFooter from './reader/ReaderFooter';
import { useReaderStore } from './reader/store';
import { useEffect } from 'react';

export default function ReaderShell({ src = '/books/example.epub' }: { src?: string }) {
  // ✅ Use separate selectors so each hook returns a stable primitive/reference
  const showChrome = useReaderStore((s) => s.showChrome);
  const setShowChrome = useReaderStore((s) => s.setShowChrome);

  useEffect(() => {
    let t: ReturnType<typeof setTimeout> | undefined;
    const onMove = () => {
      setShowChrome(true);
      if (t) clearTimeout(t);
      t = setTimeout(() => setShowChrome(false), 1500);
    };
    window.addEventListener('mousemove', onMove, { passive: true });
    return () => {
      window.removeEventListener('mousemove', onMove);
      if (t) clearTimeout(t);
    };
  }, [setShowChrome]);

  return (
    <div className="grid grid-cols-[320px_1fr] h-[100dvh]">
      <TocSidebar />
      <div className="relative bg-background">
        <div className={`pointer-events-none absolute left-1/2 -translate-x-1/2 top-2 z-20 transition-opacity ${showChrome ? 'opacity-100' : 'opacity-0'}`}>
          <ReaderHeader />
        </div>
        <div className={`pointer-events-none absolute left-1/2 -translate-x-1/2 bottom-2 z-20 transition-opacity ${showChrome ? 'opacity-100' : 'opacity-0'}`}>
          <ReaderFooter />
        </div>
        <ReaderCanvas src={src} />
      </div>
    </div>
  );
}
